<div class="w-full bg-white" [formGroup]="patientForm">
    <div class="w-full">
        <table class="table">
            <tbody>
                <tr>
                    <td>
                        <span class="font-medium">First Name <span class="text-danger">*</span></span>
                    </td>
                    <td>
                        <input type="text" class="form-control"
                               [ngClass]="{ 'is-invalid': submitted && f['txtFirstName'].errors}"
                               formControlName="txtFirstName"
                               maxlength="125"
                               placeholder="Enter First Name">
                        <div class="text-danger small mt-1"
                             *ngIf="submitted && f['txtFirstName'].errors?.['required']">
                            First Name is required
                        </div>
                        <div class="text-danger small mt-1"
                             *ngIf="submitted && f['txtFirstName'].errors?.['maxlength']">
                            Max length 125 characters
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">Last Name <span class="text-danger">*</span></span>
                    </td>
                    <td>
                        <input type="text" class="form-control"
                               [ngClass]="{ 'is-invalid': submitted && f['txtLastName'].errors}"
                               formControlName="txtLastName"
                               maxlength="125"
                               placeholder="Enter Last Name">
                        <div class="text-danger small mt-1"
                             *ngIf="submitted && f['txtLastName'].errors?.['required']">
                            Last Name is required
                        </div>
                        <div class="text-danger small mt-1"
                             *ngIf="submitted && f['txtLastName'].errors?.['cannotContainSpace']">
                            Last Name cannot contain space
                        </div>
                        <div class="text-danger small mt-1"
                             *ngIf="submitted && f['txtLastName'].errors?.['maxlength']">
                            Max length 125 characters
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">Account #</span>
                    </td>
                    <td>
                        <input type="text" class="form-control"
                               formControlName="txtAccountNo"
                               maxlength="20"
                               placeholder="Enter Account #">
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">MRN <span class="text-danger">*</span></span>
                    </td>
                    <td>
                        <input type="text" class="form-control"
                               [ngClass]="{ 'is-invalid': submitted && f['txtMRN'].errors}"
                               formControlName="txtMRN"
                               maxlength="225"
                               placeholder="Enter MRN">
                        <div class="text-danger small mt-1"
                             *ngIf="submitted && f['txtMRN'].errors?.['required']">
                            MRN is required
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">DOB <span class="text-danger">*</span></span>
                    </td>
                    <td>
                        <input type="date" class="form-control"
                               [ngClass]="{ 'is-invalid': submitted && f['txtDOB'].errors}"
                               formControlName="txtDOB"
                               [max]="maxDate"
                               placeholder="Enter DOB">
                        <div class="text-danger small mt-1"
                             *ngIf="submitted && f['txtDOB'].errors?.['required']">
                            Date of Birth is required
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">Pt. Class <span class="text-danger">*</span></span>
                    </td>
                    <td>
                        <select class="form-control"
                                [ngClass]="{ 'is-invalid': submitted && f['ddlPatientClass'].errors}"
                                formControlName="ddlPatientClass">
                            <option value="">Select Patient Class</option>
                            <option value="I">InPatient</option>
                            <option value="O">OutPatient</option>
                        </select>
                        <div class="text-danger small mt-1"
                             *ngIf="submitted && f['ddlPatientClass'].errors?.['required']">
                            Patient Class is required
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">Facility <span class="text-danger">*</span></span>
                    </td>
                    <td>
                        <select class="form-control"
                                [ngClass]="{ 'is-invalid': submitted && f['ddlFacilityName'].errors}"
                                formControlName="ddlFacilityName"
                                (change)="getPhysiciansByFacility($any($event.target).value)">
                            <option value="">Choose Facility Name</option>
                            <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                                {{s.facilityName}}
                            </option>
                        </select>
                        <div class="text-danger small mt-1"
                             *ngIf="submitted && f['ddlFacilityName'].errors?.['required']">
                            Facility Name is required
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">Admit <span class="text-danger">*</span></span>
                    </td>
                    <td>
                        <mat-form-field class="w-100">
                            <mtx-datetimepicker #datetimePicker [type]="type" [mode]="mode"
                                [multiYearSelector]="multiYearSelector" [startView]="startView"
                                [twelvehour]="twelvehour" [timeInterval]="timeInterval"
                                [touchUi]="touchUi" [timeInput]="timeInput">
                            </mtx-datetimepicker>
                            <input [mtxDatetimepicker]="datetimePicker"
                                   formControlName="txtaddAdmissionDate"
                                   matInput
                                   class="form-control"
                                   [ngClass]="{ 'is-invalid': submitted && f['txtaddAdmissionDate'].errors}"
                                   placeholder="Select Admission Date">
                            <mtx-datetimepicker-toggle [for]="datetimePicker" matSuffix></mtx-datetimepicker-toggle>
                        </mat-form-field>
                        <div class="text-danger small mt-1"
                             *ngIf="submitted && f['txtaddAdmissionDate'].errors?.['required']">
                            Admission Date is required
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">Assign To <span class="text-danger">*</span></span>
                    </td>
                    <td>
                        <select class="form-control"
                                [ngClass]="{ 'is-invalid': submitted && f['ddlPhysician'].errors}"
                                formControlName="ddlPhysician">
                            <option value="">Choose Physician</option>
                            <option [value]="s.physicianName" *ngFor="let s of listOfPhysicians">
                                {{s.physicianName}}
                            </option>
                        </select>
                        <div class="text-danger small mt-1"
                             *ngIf="submitted && f['ddlPhysician'].errors?.['required']">
                            Physician assignment is required
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">Department <span class="text-danger">*</span></span>
                    </td>
                    <td>
                        <input type="text" class="form-control"
                               [ngClass]="{ 'is-invalid': submitted && f['txtDepartment'].errors}"
                               formControlName="txtDepartment"
                               maxlength="100"
                               placeholder="Enter Department">
                        <div class="text-danger small mt-1"
                             *ngIf="submitted && f['txtDepartment'].errors?.['required']">
                            Department is required
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">Room <span class="text-danger">*</span></span>
                    </td>
                    <td>
                        <input type="text" class="form-control"
                               [ngClass]="{ 'is-invalid': submitted && f['txtRoomNo'].errors}"
                               formControlName="txtRoomNo"
                               maxlength="100"
                               placeholder="Enter Room">
                        <div class="text-danger small mt-1"
                             *ngIf="submitted && f['txtRoomNo'].errors?.['required']">
                            Room Number is required
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">Bed <span class="text-danger">*</span></span>
                    </td>
                    <td>
                        <input type="text" class="form-control"
                               [ngClass]="{ 'is-invalid': submitted && f['txtBedNo'].errors}"
                               formControlName="txtBedNo"
                               maxlength="100"
                               placeholder="Enter Bed">
                        <div class="text-danger small mt-1"
                             *ngIf="submitted && f['txtBedNo'].errors?.['required']">
                            Bed Number is required
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="font-medium">SSN</span>
                    </td>
                    <td>
                        <input type="text" class="form-control"
                               [ngClass]="{ 'is-invalid': submitted && f['txtSSN'].errors}"
                               formControlName="txtSSN"
                               maxlength="9"
                               placeholder="Enter SSN">
                        <div class="text-danger small mt-1"
                             *ngIf="submitted && f['txtSSN'].errors?.['maxLength']">
                            Max length 9 characters
                        </div>
                        <div class="text-danger small mt-1"
                             *ngIf="submitted && f['txtSSN'].errors?.['pattern']">
                            SSN should be a number
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="upload-section upload-footer">
        <div class="flex flex-col">
            <button type="button" class="cancel-btn mb-2" (click)="clearData()">Cancel</button>
            <button type="button" class="upload-btn" (click)="insertPatient()">Add Patient</button>
        </div>
    </div>
</div>