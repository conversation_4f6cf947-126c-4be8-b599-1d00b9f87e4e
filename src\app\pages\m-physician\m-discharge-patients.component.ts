import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormControl } from '@angular/forms';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { PhysicianService } from 'src/app/services/physician/physician.service';

@Component({
  selector: 'app-m-discharge-patients',
  templateUrl: './m-discharge-patients.component.html',
  styleUrls: ['./m-physician.scss']
})
export class MDischargePatientsComponent implements OnInit {
  public submitted: boolean = false;
  public FilterForm: FormGroup;
  public listOfFacilities: Array<any> = [];
  public request: any = {};
  public p: number = 1;
  public listOfPatients: Array<any> = [];
  public timeout: any = null;
  public totalCount: number = 0;
  device = false;
  public historyTotalCount: number = 0;
  public filterObj: any = {};
  orderBy = 'desc';
  sortColumnBy = 'admission_Date';
  public searchByName: string = '';
  constructor(private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService
    , private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly physicianService: PhysicianService) { }

  ngOnInit() {
    this.appComp.loadPageName('Discharge Patients', 'physicianTab');
    this.FilterForm = this.fb.group({
      ddlFacility: ['', [Validators.required]],
      txtFromDate: ['', [Validators.required, endDateValidator]],
      txtToDate: ['', [Validators.required, endDateValidator]]
    });
    if (history.state.filterObj) {
      this.filterObj = history.state.filterObj;
      this.FilterForm.get('ddlFacility')?.setValue(this.filterObj.ddlFacility);
      this.FilterForm.get('txtFromDate')?.setValue(this.filterObj.txtFromDate);
      this.FilterForm.get('txtToDate')?.setValue(this.filterObj.txtToDate);
      this.searchByName = this.filterObj.searchByName;
      this.p = this.filterObj.p;
      this.getDischargePatientData(this.p);
    }
    if (/Android|webOS|iPhone|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) && (window.innerWidth <= 575 || window.innerWidth >= 575)) {
      // some code..
      this.device = true;
    } else {
      this.device = false;
    }
    this.getFacilities();
  }
  get f() { return this.FilterForm.controls; }

  clearFilter() {
    this.submitted = false;
    this.FilterForm.get('txtFromDate')?.setValue('');
    this.FilterForm.get('txtToDate')?.setValue('');
  }

  getFacilities() {
    this.commonServ.startLoading();
    this.commonServ.getFacilitiesByUserType('PHYSICIAN').subscribe((p: any) => {
      this.listOfFacilities = p;
      this.commonServ.stopLoading();
    }, error => { console.error(error.status); });
  }
  updateSortObj(obj: any) {
    this.sortColumnBy = obj.sortColumnBy;
    this.orderBy = obj.orderBy;
  }
  getDischargePatientData(pno) {
    this.p = pno;
    this.submitted = true;
    if (this.FilterForm.invalid) {
      return;
    }
    this.commonServ.startLoading();
    this.request.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.DischargeFromDate = this.encrDecr.set(this.FilterForm.value.txtFromDate);
    this.request.DischargeToDate = this.encrDecr.set(this.FilterForm.value.txtToDate);
    this.request.sPageSize = this.encrDecr.set('14');
    this.request.sPageIndex = this.encrDecr.set(pno);
    this.request.Searchstring = this.encrDecr.set(this.searchByName);
    this.request.SortColumn = this.encrDecr.set(this.sortColumnBy);
    this.request.SortExpression = this.encrDecr.set(this.orderBy);
    this.physicianService.getDischargePatientData(this.request).subscribe((p: any) => {
      this.listOfPatients = p.physicianPatientsResponses;
      this.totalCount = p.myPatientCount;
      this.request = {};
      this.commonServ.stopLoading();
    }, error => { console.error(error.status); }
    )
  }

  onKeyPatientSearch() {
    if (this.FilterForm.invalid) {
      return;
    }
    this.getDischargePatientData(1);
  }

}

export function endDateValidator(control: FormControl) {
  let startDate = control.root.get('txtFromDate');
  let endDate = control.root.get('txtToDate');
  if (startDate && endDate) {
    if (startDate.value && endDate.value) {
      let startDateVal = new Date(startDate.value);
      let endDateVal = new Date(endDate.value);
      let errorObj = { 'txtFromDate': false, 'txtToDate': false };
      let isEndDateValid = (startDateVal < endDateVal || startDateVal.getDate() == endDateVal.getDate());
      endDate.setErrors((!isEndDateValid)
        ? errorObj : null);
      startDate.setErrors((!isEndDateValid)
        ? errorObj : null);
      return control.errors ? { "endDateError": true } : null;
    }
  }
  return null;
}

