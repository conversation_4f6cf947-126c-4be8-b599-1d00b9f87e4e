<div class="patient-list-container">
    <!-- Header Section -->
    <div class="header-section" [formGroup]="FilterForm">
        <!-- Search Bar -->
        <div class="search-container">
            <div class="search-input-wrapper">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="Search Name, Account, MRN, Room"
                    formControlName="txtSearchKey" (keyup)="onKeyPatientSearch()">
            </div>
            <button class="sort-btn">
                <img src="assets/icons/icon-sort.svg" class="img-icon">
            </button>
            <button class="filter-btn" (click)="onKeyPatientSearch()">
                <img src="assets/icons/icon-filter-active.svg" class="img-icon">
            </button>
        </div>

        <!-- Filter Tabs -->
        <div class="filter-tabs">
            <div class="facility-dropdown">
                <select id="ddlFacility" class="form-control" formControlName="ddlFacility"
                    (change)="facilityChangeEvent()" [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                    <option value="">---Select Facility---</option>
                    <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                        <span>{{s.facilityName}}</span>
                    </option>
                </select>
            </div>
            <div class="facility-dropdown">
                <select id="ddlDepartment" class="form-control" formControlName="ddlDepartment"
                    (change)="getHospitalCensusData(1)"
                    [ngClass]="{ 'is-invalid': submitted && f['ddlDepartment'].errors}">
                    <option value="All">All Departments</option>
                    <option [value]="s.patienT_LOCATION" *ngFor="let s of listofDepartments">
                        {{s.patienT_LOCATION}}
                    </option>
                </select>
            </div>
        </div>
    </div>

    <!-- Patient Cards Starts -->
    <app-m-patients [listOfPatients]="listOfPatients" [p]="p" [pageName]="'/physician/hospital-census'"
        [FilterForm]="FilterForm" [totalCount]="totalCount" [searchByName]="''" [device]="device"
        (enventUpdateSortObj)="updateSortObj($event)" [orderBy]="orderBy" [sortColumnBy]="sortColumnBy"
        (eventListOfPatients)="getHospitalCensusData($event)"></app-m-patients>
    <!-- Patient Cards Ends -->

</div>