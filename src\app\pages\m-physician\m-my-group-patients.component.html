<div class="patient-list-container">
    <!-- Header Section -->
    <div class="header-section" [formGroup]="FilterForm">
        <!-- Search Bar -->
        <div class="search-container">
            <div class="search-input-wrapper">
                <i class="fas fa-search search-icon" (click)="onKeyPatientSearch()"></i>
                <input type="text" class="search-input" placeholder="Search Name, Account, MRN, Room"
                    [(ngModel)]="searchByName" [ngModelOptions]="{standalone: true}">
            </div>
            <button class="sort-btn">
                <img src="assets/icons/icon-sort.svg" class="img-icon">
            </button>
            <button class="filter-btn" (click)="onKeyPatientSearch()">
                <img src="assets/icons/icon-filter-active.svg" class="img-icon">
            </button>
        </div>

        <!-- Filter Tabs -->
        <div class="filter-tabs">
            <div class="facility-dropdown">
                <select id="ddlFacility" class="form-control" formControlName="ddlFacility"
                    (change)="FilterPatientDetails()" [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                    <option value="">---Select Facility---</option>
                    <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                        <span>{{s.facilityName}}</span>
                    </option>
                </select>
            </div>
            <div class="facility-dropdown">
                <select id="ddlGroups" class="form-control" formControlName="ddlGroups" (change)="getphyciansBygroup()"
                    [ngClass]="{ 'is-invalid': submitted && f['ddlGroups'].errors}">
                    <option value="">---Select Group---</option>
                    <option [value]="s.group_name" *ngFor="let s of listOfGroups">
                        <span>{{s.group_name}}</span>
                    </option>
                </select>
            </div>
        </div>
        <div class="filter-tabs mt-2">
            <div class="facility-dropdown">
                <ng-multiselect-dropdown class="form-control" (onSelect)="FilterPatientDetails()"
                    (onDeSelect)="FilterPatientDetails()" (onSelectAll)="getphyciansBygroupPhyAll()"
                    (onDeSelectAll)="getphyciansBygroupPhyAll()" [placeholder]="'---Select Physician---'"
                    formControlName="ddlphybygroup" [data]="listOfPhysiciansandcount" [settings]="mDdlPhysicianSettings"
                    [ngModel]="selectedListOfPhysiciansandcount">
                </ng-multiselect-dropdown>
            </div>
        </div>
    </div>

    <!-- Patient Cards Starts -->
    <app-m-patients [listOfPatients]="listOfPatients" [p]="p" [pageName]="'/physician/my-group-patients'"
        [FilterForm]="FilterForm" [totalCount]="totalCount" [searchByName]="searchByName" [device]="device"
        (enventUpdateSortObj)="updateSortObj($event)" [orderBy]="orderBy" [sortColumnBy]="sortColumnBy"
        (eventListOfPatients)="getMyGroupPatientsDetails($event)"
        (eventRemovePatient)="RemoveGroupForPatient($event)"></app-m-patients>
    <!-- Patient Cards Ends -->

</div>