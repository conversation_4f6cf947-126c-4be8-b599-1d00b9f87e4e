<div class="patient-list-container">
    <!-- Header Section -->
    <div class="header-section" [formGroup]="FilterForm">
        <!-- Search Bar -->
        <div class="search-container">
            <div class="search-input-wrapper">
                <i class="fas fa-search search-icon" (click)="onKeyPatientSearch()"></i>
                <input type="text" class="search-input" placeholder="Search Name, Account, MRN, Room"
                 [(ngModel)]="searchByName" [ngModelOptions]="{standalone: true}">
            </div>
            <button class="sort-btn">
                <img src="assets/icons/icon-sort.svg" class="img-icon">
            </button>
            <button class="filter-btn" (click)="onKeyPatientSearch()">
                <img src="assets/icons/icon-filter-active.svg" class="img-icon">
            </button>
        </div>

        <!-- Filter Tabs -->
        <div class="filter-tabs">
            <div class="facility-dropdown">
                <select id="ddlFacility" class="form-control" formControlName="ddlFacility" (change)="FilterPatientDetails()"
                    [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                    <option value="">---Select Facility---</option>
                    <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                        <span>{{s.facilityName}}</span>
                    </option>
                </select>
            </div>
            <div class="tabs">
                <button class="tab-btn active" [class.active]="activeTab === 'myPatients'"
                    (click)="setActiveTab('myPatients')">
                    My Patients
                </button>
                <button class="tab-btn" [class.active]="activeTab === 'hidden'" (click)="setActiveTab('hidden')">
                    Hidden
                </button>
            </div>
        </div>
    </div>

    <!-- Patient Cards Starts -->
    <app-m-patients [listOfPatients]="listOfPatients" [p]="p" [pageName]="'/m/physician/my-patients'"
        [FilterForm]="FilterForm" [totalCount]="messageCount" [searchByName]="searchByName" [device]="device"
        (enventUpdateSortObj)="updateSortObj($event)" [orderBy]="orderBy" [sortColumnBy]="sortColumnBy"
        (eventListOfPatients)="childListOfPatients($event)" (eventRemovePatient)="RemovePatient($event)"></app-m-patients>
    <!-- Patient Cards Ends -->

</div>