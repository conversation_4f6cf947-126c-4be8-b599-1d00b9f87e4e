<div class="patient-cards">
    <div class="patient-card p-3 flex flex-col" *ngFor="let item of listOfPatients">
        <div class="flex flex-col p-6">
            <div class="flex">
                <span class="text-lg font-medium"
                    [ngClass]="{'hasTodayEncounter':(item.hasPreviousEncounter=='1' && item.resetPriorEncounterStatus=='1'),'hasDraftEncounter':(item.hasPreviousEncounter=='1' && item.encounteR_ID!='0' && item.encounteR_ID!=null),'hasPriorEncounter':(item.hasPreviousEncounter=='1' && item.encounteR_ID==null && item.resetPriorEncounterStatus==null),'hasNoEncounter':(item.hasPreviousEncounter=='0')}">
                    {{item.patient_Name}}</span>
                <div class="mx-2 h-6 border-l-2"></div>
                <span class="font-normal">{{item.dbo}} ({{item.age}} {{item.sex}})</span>
                <div class="mx-2 h-6 border-l-2"></div>
                <span class="font-normal">{{item.facility_Name}}</span>
            </div>
            <div class="my-2 h-1 w-12 border-t-2"></div>
            <div class="grid w-full grid-cols-2 gap-x-4">
                <div>{{item.account_Number}}</div>
                <div>{{item.room_Number}}</div>
                <div>{{item.admission_Type}}</div>
                <div class="flex items-center">
                    <span [style.border-color]="item.color">
                        {{item.arithmetic_Mean_LOS}}</span>
                    <!-- <div class="mx-2 h-4 border-l-2"></div>
                        <span>GLOS:4</span> -->
                </div>
            </div>
            <div class="mt-2 flex items-center">
                <div>
                    <img src="assets/icons/icon-price-transparency.svg" class="img-icon">
                </div>
                <div class="ml-2">
                    {{item.reimbursement_Type}}
                </div>
            </div>
            <div class="mt-2 flex items-center">
                <div>
                    <img src="assets/icons/icon-hc-Stethoscope-Doctor.svg" class="img-icon">
                </div>
                <div class="ml-2">
                    {{item.attending_Physician_InApp}}
                </div>
            </div>
        </div>
        <div class="action-buttons">
            <button class="action-btn delete-btn"
                *ngIf="pageName=='/m/physician/my-group-patients' || pageName=='/m/physician/my-patients'"
                title="Remove patient" (click)='RemovePatient(item)'>
                <img src="assets/icons/icon-trash-red.svg" class="img-icon px-8">
            </button>
            <button class="action-btn lightning-btn" *ngIf='item.isHide==0' title="Hide" (click)='HidePatient(item)'>
                <img src="assets/icons/icon-hide.svg" class="img-icon px-8">
            </button>
            <button class="action-btn lightning-btn" *ngIf='item.isHide==1' title="Un-Hide"
                (click)='UnHidePatient(item)'>
                <img src="assets/icons/icon-eye.svg" class="img-icon px-8">
            </button>
            <button class="action-btn refresh-btn" [matMenuTriggerFor]="summaryMenu">
                <img src="assets/icons/icon-change.svg" class="img-icon px-6">
            </button>
            <button class="action-btn play-btn" *ngIf="residentAccess=='YES';else resEls"
                [routerLink]="['/m/physician/approve-pending-encounter']" [state]="{patient:item,backUrl:pageName}">
                <img src="assets/icons/icon-start.svg" class="img-icon px-8">
            </button>
            <ng-template #resEls>
                <button class="action-btn play-btn" [routerLink]="['/m/physician/start-new-encounter']"
                    [state]="{patient:item,encounterSeenDate:encounterSeenDate,backUrl:pageName,facilityType:item.isPrimeFacility,filterObj:{p:p,searchByName:searchByName,ddlFacility:FilterForm.value.ddlFacility,ddlGroups:FilterForm.value.ddlGroups,ddlphybygroup:FilterForm.value.ddlphybygroup,ddlPatients:FilterForm.value.ddlPatients,ddlDepartment:FilterForm.value.ddlDepartment,txtFromDate:FilterForm.value.txtFromDate,txtToDate:FilterForm.value.txtToDate}}">
                    <img src="assets/icons/icon-start.svg" class="img-icon px-8">
                </button>
            </ng-template>
            <button class="action-btn menu-btn" (click)="openActionsPopup()">
                <img src="assets/icons/icon-more.svg" class="img-icon">
            </button>
        </div>

        <!-- Mat Actions Starts -->
        <mat-menu #summaryMenu="matMenu">
            <button mat-menu-item
                *ngIf="(pageName=='/physician/my-group-patients' || pageName=='/physician/hospital-census') && !item.attending_Physician_InApp.includes(userName)"
                title="Assign to my list" (click)='assignToMyListSave(item)'>
                <img src="assets/icons/icon-change.svg" class="img-icon">
                Assign to My List
            </button>
            <button mat-menu-item title="Assign to Others" (click)='assignToOthersPopup(item)'>
                <img src="assets/icons/icon-change.svg" class="img-icon">
                Assign to Others
            </button>
            <button mat-menu-item *ngIf='item.isResidentAcc==1 || item.isResidents==1' title="Assign to Resident"
                (click)='assignToResidentPopup(item)'>
                <img src="assets/icons/icon-change.svg" class="img-icon">
                Assign to Resident
            </button>
        </mat-menu>
        <!-- Mat Actions Starts -->

        <!-- Actions Popup Starts -->
        <div *ngIf="showActionsPopup">
            <div class="modal-backdrop"></div>
            <div class="actions-popup" [@slideUp]>
                <div class="actions-header">
                    <div class="flex flex-col">
                        <span>Actions</span>
                        <span class="actions-subtitle">Select actions to perform on {{item.patient_Name}}</span>
                    </div>
                    <button class="close-btn" (click)="closeActionsPopup()">
                        <img src="assets/icons/icon-close.svg" class="img-icon">
                    </button>
                </div>
                <div class="max-h-70">
                    <div class="actions-card">
                        <div class="flex flex-col">
                            <div class="action-item px-4 py-3 border-b">
                                <div class="flex flex-col">
                                    Past History
                                </div>
                                <div>
                                    <img src="assets/icons/icon-history.svg" class="action-icon">
                                </div>
                            </div>
                            <div class="action-item px-4 py-3 border-b">
                                <div class="flex flex-col">
                                    Notes
                                </div>
                                <div>
                                    <img src="assets/icons/icon-note.svg" class="action-icon">
                                </div>
                            </div>
                            <div class="action-item px-4 py-3 border-b">
                                <div class="flex flex-col">
                                    Attachments
                                </div>
                                <div>
                                    <img src="assets/icons/icon-attachment.svg" class="action-icon">
                                </div>
                            </div>
                            <div class="action-item px-4 py-3">
                                <div class="flex flex-col">
                                    Edit Patient Details
                                </div>
                                <div>
                                    <img src="assets/icons/icon-edit.svg" class="action-icon">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="actions-card mt-4">
                        <div class="flex flex-col">
                            <div class="action-item px-4 py-3 border-b">
                                <div class="flex flex-col">
                                    Posted to Biller
                                </div>
                                <div>
                                    <img src="assets/icons/icon-reimbursement.svg" class="action-icon">
                                </div>
                            </div>
                            <div class="action-item px-4 py-3">
                                <div class="flex flex-col">
                                    Close Hospitalization
                                </div>
                                <div>
                                    <img src="assets/icons/icon-close-circle-outline.svg" class="action-icon">
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Actions Popup Ends -->

    </div>
</div>