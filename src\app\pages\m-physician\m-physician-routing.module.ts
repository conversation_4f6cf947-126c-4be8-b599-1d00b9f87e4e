import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MMyPatientsComponent } from './m-my-patients.component';
import { MsalGuard } from '@azure/msal-angular';
import { MMyGroupPatientsComponent } from './m-my-group-patients.component';
import { MHospitalCensusComponent } from './m-hospital-census.component';
import { MAddPatientComponent } from './m-add-patient.component';
import { MDischargePatientsComponent } from './m-discharge-patients.component';
import { MPendingApprovalEncountersComponent } from './m-pending-approval-encounters.component';
import { MUnbilledEncountersComponent } from './m-unbilled-encounters.component';
import { MApprovePendingEncounterComponent } from './m-approve-pending-encounter.component';
import { MStartNewEncounterComponent } from './m-start-new-encounter.component';

const routes: Routes = [
  { path: "my-patients", component: MMyPatientsComponent, canActivate: [MsalG<PERSON>] },
  { path: "my-group-patients", component: MMyGroupPatientsComponent, canActivate: [MsalGuard] },
  { path: "hospital-census", component: MHospitalCensusComponent, canActivate: [MsalGuard] },
  { path: "discharge-patients", component: MDischargePatientsComponent, canActivate: [MsalGuard] },
  { path: "pending-approval-encounters", component: MPendingApprovalEncountersComponent, canActivate: [MsalGuard] },
  { path: "add-patient", component: MAddPatientComponent, canActivate: [MsalGuard] },
  { path: "unbilled-encounters", component: MUnbilledEncountersComponent, canActivate: [MsalGuard] },
  { path: "start-new-encounter", component: MStartNewEncounterComponent, canActivate: [MsalGuard] },
  { path: "approve-pending-encounter", component: MApprovePendingEncounterComponent, canActivate: [MsalGuard] }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MPhysicianRoutingModule { }
