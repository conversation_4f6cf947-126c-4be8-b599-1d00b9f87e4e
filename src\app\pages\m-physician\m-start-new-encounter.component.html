<div class="details-header">
    <button class="back-btn">
        <img src="assets/icons/icon-caret-Left.svg" class="img-icon">
    </button>
    <div class="patient-name ">{{patient.patient_Name}} {{patient.sex}} ({{patient.sex}}Y)</div>
</div>

<div class="w-full bg-white">
    <mat-tab-group (selectedTabChange)="onMatGroupTabClick($event)">
        <mat-tab label="Details">
            <ng-template matTabContent>
                <div class="w-full p-4">
                    <table class="table table-striped">
                        <tbody>
                            <tr>
                                <td>Account</td>
                                <td><span class="font-medium">{{patient.account_Number}}</span></td>
                            </tr>
                            <tr>
                                <td>Room</td>
                                <td><span class="font-medium">{{patient.room_Number}}</span></td>
                            </tr>
                            <tr>
                                <td>Type</td>
                                <td><span class="font-medium">{{patient.admission_Type}}</span></td>
                            </tr>
                            <tr>
                                <td>DOB</td>
                                <td><span class="font-medium">{{patient.dbo}}</span></td>
                            </tr>
                            <tr>
                                <td>LOS</td>
                                <td><span class="font-medium">{{patient.arithmetic_Mean_LOS}}</span></td>
                            </tr>
                            <tr>
                                <td>Attending</td>
                                <td><span class="font-medium">{{patient.attending_Physician_InApp}}</span></td>
                            </tr>
                            <tr>
                                <td>Payor</td>
                                <td><span class="font-medium">{{patient.reimbursement_Type}}</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="w-full mt-4">
                        <button class="mark-seen-btn" (click)="viewSubmitEncounterPop()">Mark as seen</button>
                        <div class="details-buttons mt-2">
                            <button class="details-btn" (click)="viewPastEncounters()">
                                <img src="assets/icons/icon-history.svg" class="img-icon">
                            </button>
                            <button class="details-btn">
                                <img src="assets/icons/icon-note.svg" class="img-icon">
                            </button>
                            <button class="details-btn" (click)="viewAttachments()">
                                <img src="assets/icons/icon-attachment.svg" class="img-icon">
                            </button>
                        </div>
                    </div>
                </div>
                <div class="code-content">
                    <!-- Subgroups starts -->
                    <div class="patient-card flex flex-col p-3">
                        <div class="flex justify-between mb-2">
                            <span class="font-medium px-2">Subgroups</span>
                            <span class="ml-3">
                                <img alt="" src="../../../assets/info-icon.svg" width="15" />
                                <span class="small aria-label m-1"> Defaults to {{testData.group_name}} group if no
                                    subgroup is selected</span>
                            </span>
                        </div>
                        <div class="flex flex-col">
                            <div class="filter-tabs">
                                <div class="facility-dropdown">
                                    <select class="form-control">
                                        <option>Select Subgroups</option>
                                        <option>Subgroups 1</option>
                                        <option>Subgroups 2</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Subgroups ends -->
                    <div class="patient-card flex flex-col p-3">
                        <div class="flex justify-between mb-2">
                            <span class="font-medium px-2">CPT Codes</span>
                            <button class="close-btn" (click)="viewCPTCodes()">
                                <img src="assets/icons/icon-pluscircle-solid-s.svg" class="img-icon">
                            </button>
                        </div>
                        <div class="flex flex-col">
                            <div class="p-2 border-b">
                                <div class="flex items-center">
                                    <button class="close-btn">
                                        <img src="assets/icons/icon-trash.svg" class="img-icon">
                                    </button>
                                    <div class="flex flex-col ml-2">
                                        <div>99396 - Preventive medicine visit, established patient, ages
                                            40-64.</div>
                                        <div class="text-link">Add/Edit modifiers</div>
                                    </div>
                                </div>
                            </div>
                            <div class="p-2">
                                <div class="flex items-center">
                                    <button class="close-btn">
                                        <img src="assets/icons/icon-trash.svg" class="img-icon">
                                    </button>
                                    <div class="flex flex-col ml-2">
                                        <div>28170 - Arthrodesis, great toe.</div>
                                        <div class="text-link">Add/Edit modifiers</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="code-section">
                        <div class="code-item" (click)="viewCPTCodes()">
                            <div class="code-icon cpt-icon">
                                <img src="assets/icons/icon-PlusCircle-s.svg" class="img-icon">
                            </div>
                            <div class="code-desc">
                                <span>Add new CPT codes</span>
                                <span class="chevron">
                                    <img src="assets/icons/icon-caret-Right.svg" class="img-icon">
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="patient-card flex flex-col p-3">
                        <div class="flex justify-between mb-2">
                            <span class="font-medium px-2">ICD Codes</span>
                            <button class="close-btn">
                                <img src="assets/icons/icon-pluscircle-solid-b.svg" class="img-icon">
                            </button>
                        </div>
                        <div class="flex flex-col">
                            <div class="p-2 border-b">
                                <div class="flex items-center">
                                    <button class="close-btn">
                                        <img src="assets/icons/icon-trash.svg" class="img-icon">
                                    </button>
                                    <div class="flex flex-col ml-2">
                                        <div>99396 - Preventive medicine visit, established patient, ages
                                            40-64.</div>
                                    </div>
                                </div>
                            </div>
                            <div class="p-2">
                                <div class="flex items-center">
                                    <button class="close-btn">
                                        <img src="assets/icons/icon-trash.svg" class="img-icon">
                                    </button>
                                    <div class="flex flex-col ml-2">
                                        <div>28170 - Arthrodesis, great toe.</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="code-section">
                        <div class="code-item">
                            <div class="code-icon icd-icon">
                                <img src="assets/icons/icon-PlusCircle-b.svg" class="img-icon">
                            </div>
                            <div class="code-desc">
                                <span>Add new ICD codes</span>
                                <span class="chevron">
                                    <img src="assets/icons/icon-caret-Right.svg" class="img-icon">
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </ng-template>
        </mat-tab>
        <mat-tab label="History">
            <ng-template matTabContent>
                <app-m-view-history [listOfPatientHistory]='listOfPatientHistory' [patient]='patient'
                    [historyTotalCount]="historyTotalCount"></app-m-view-history>
            </ng-template>
        </mat-tab>
        <mat-tab label="Note">
            <ng-template matTabContent>
                <p>Notes</p>
            </ng-template>
        </mat-tab>
        <mat-tab label="Attachments">
            <ng-template matTabContent>
                <p>Attachments</p>
            </ng-template>
        </mat-tab>
    </mat-tab-group>
</div>

<!-- Encounters Popup -->
<div *ngIf="showEncountersPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideUp]>
        <div class="actions-header">
            <span>Past Encounters</span>
            <button class="close-btn" (click)="closeEncountersPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-70">
            <div class="patient-cards">
                <div class="patient-card flex flex-col">
                    <div class="text-center bg-gray-200 p-2">
                        12/17/2024 01:15:45 AM
                    </div>
                    <div class="flex flex-col p-3 cursor-pointer" (click)="viewEncountersDetails()">
                        <div class="flex">
                            <span class="text-lg font-medium">Shark Watermelon</span>
                            <div class="mx-2 h-6 border-l-2"></div>
                            <span class="font-normal">02/02/79 (46 F)</span>
                            <div class="mx-2 h-6 border-l-2"></div>
                            <span class="font-normal">TRMC</span>
                        </div>
                        <div class="my-2 h-1 w-12 border-t-2"></div>
                        <div class="grid w-full grid-cols-2 gap-x-4">
                            <div>V00000000000123</div>
                            <div>022OT-B</div>
                            <div>Inpatient</div>
                            <div class="flex items-center">
                                <span>LOS:2</span>
                                <div class="mx-2 h-4 border-l-2"></div>
                                <span>GLOS:4</span>
                            </div>
                        </div>
                        <div class="mt-2 flex items-center">
                            <div>
                                <img src="assets/icons/icon-hc-Stethoscope-Doctor.svg" class="img-icon">
                            </div>
                            <div class="ml-2">
                                Zebra Cantaloupe, Tiger Cherry
                            </div>
                        </div>
                    </div>
                </div>
                <div class="patient-card flex flex-col">
                    <div class="text-center bg-gray-200 p-2">
                        12/17/2024 01:15:45 AM
                    </div>
                    <div class="flex flex-col p-3 cursor-pointer" (click)="viewEncountersDetails()">
                        <div class="flex">
                            <span class="text-lg font-medium">Shark Watermelon</span>
                            <div class="mx-2 h-6 border-l-2"></div>
                            <span class="font-normal">02/02/79 (46 F)</span>
                            <div class="mx-2 h-6 border-l-2"></div>
                            <span class="font-normal">TRMC</span>
                        </div>
                        <div class="my-2 h-1 w-12 border-t-2"></div>
                        <div class="grid w-full grid-cols-2 gap-x-4">
                            <div>V00000000000123</div>
                            <div>022OT-B</div>
                            <div>Inpatient</div>
                            <div class="flex items-center">
                                <span>LOS:2</span>
                                <div class="mx-2 h-4 border-l-2"></div>
                                <span>GLOS:4</span>
                            </div>
                        </div>
                        <div class="mt-2 flex items-center">
                            <div>
                                <img src="assets/icons/icon-hc-Stethoscope-Doctor.svg" class="img-icon">
                            </div>
                            <div class="ml-2">
                                Zebra Cantaloupe, Tiger Cherry
                            </div>
                        </div>
                    </div>
                </div>
                <div class="patient-card flex flex-col">
                    <div class="text-center bg-gray-200 p-2">
                        12/17/2024 01:15:45 AM
                    </div>
                    <div class="flex flex-col p-3 cursor-pointer" (click)="viewEncountersDetails()">
                        <div class="flex">
                            <span class="text-lg font-medium">Shark Watermelon</span>
                            <div class="mx-2 h-6 border-l-2"></div>
                            <span class="font-normal">02/02/79 (46 F)</span>
                            <div class="mx-2 h-6 border-l-2"></div>
                            <span class="font-normal">TRMC</span>
                        </div>
                        <div class="my-2 h-1 w-12 border-t-2"></div>
                        <div class="grid w-full grid-cols-2 gap-x-4">
                            <div>V00000000000123</div>
                            <div>022OT-B</div>
                            <div>Inpatient</div>
                            <div class="flex items-center">
                                <span>LOS:2</span>
                                <div class="mx-2 h-4 border-l-2"></div>
                                <span>GLOS:4</span>
                            </div>
                        </div>
                        <div class="mt-2 flex items-center">
                            <div>
                                <img src="assets/icons/icon-hc-Stethoscope-Doctor.svg" class="img-icon">
                            </div>
                            <div class="ml-2">
                                Zebra Cantaloupe, Tiger Cherry
                            </div>
                        </div>
                    </div>
                </div>
                <div class="patient-card flex flex-col">
                    <div class="text-center bg-gray-200 p-2">
                        12/17/2024 01:15:45 AM
                    </div>
                    <div class="flex flex-col p-3 cursor-pointer" (click)="viewEncountersDetails()">
                        <div class="flex">
                            <span class="text-lg font-medium">Shark Watermelon</span>
                            <div class="mx-2 h-6 border-l-2"></div>
                            <span class="font-normal">02/02/79 (46 F)</span>
                            <div class="mx-2 h-6 border-l-2"></div>
                            <span class="font-normal">TRMC</span>
                        </div>
                        <div class="my-2 h-1 w-12 border-t-2"></div>
                        <div class="grid w-full grid-cols-2 gap-x-4">
                            <div>V00000000000123</div>
                            <div>022OT-B</div>
                            <div>Inpatient</div>
                            <div class="flex items-center">
                                <span>LOS:2</span>
                                <div class="mx-2 h-4 border-l-2"></div>
                                <span>GLOS:4</span>
                            </div>
                        </div>
                        <div class="mt-2 flex items-center">
                            <div>
                                <img src="assets/icons/icon-hc-Stethoscope-Doctor.svg" class="img-icon">
                            </div>
                            <div class="ml-2">
                                Zebra Cantaloupe, Tiger Cherry
                            </div>
                        </div>
                    </div>
                </div>
                <div class="patient-card flex flex-col">
                    <div class="text-center bg-gray-200 p-2">
                        12/17/2024 01:15:45 AM
                    </div>
                    <div class="flex flex-col p-3 cursor-pointer" (click)="viewEncountersDetails()">
                        <div class="flex">
                            <span class="text-lg font-medium">Shark Watermelon</span>
                            <div class="mx-2 h-6 border-l-2"></div>
                            <span class="font-normal">02/02/79 (46 F)</span>
                            <div class="mx-2 h-6 border-l-2"></div>
                            <span class="font-normal">TRMC</span>
                        </div>
                        <div class="my-2 h-1 w-12 border-t-2"></div>
                        <div class="grid w-full grid-cols-2 gap-x-4">
                            <div>V00000000000123</div>
                            <div>022OT-B</div>
                            <div>Inpatient</div>
                            <div class="flex items-center">
                                <span>LOS:2</span>
                                <div class="mx-2 h-4 border-l-2"></div>
                                <span>GLOS:4</span>
                            </div>
                        </div>
                        <div class="mt-2 flex items-center">
                            <div>
                                <img src="assets/icons/icon-hc-Stethoscope-Doctor.svg" class="img-icon">
                            </div>
                            <div class="ml-2">
                                Zebra Cantaloupe, Tiger Cherry
                            </div>
                        </div>
                    </div>
                </div>
                <div class="patient-card flex flex-col">
                    <div class="text-center bg-gray-200 p-2">
                        12/17/2024 01:15:45 AM
                    </div>
                    <div class="flex flex-col p-3 cursor-pointer" (click)="viewEncountersDetails()">
                        <div class="flex">
                            <span class="text-lg font-medium">Shark Watermelon</span>
                            <div class="mx-2 h-6 border-l-2"></div>
                            <span class="font-normal">02/02/79 (46 F)</span>
                            <div class="mx-2 h-6 border-l-2"></div>
                            <span class="font-normal">TRMC</span>
                        </div>
                        <div class="my-2 h-1 w-12 border-t-2"></div>
                        <div class="grid w-full grid-cols-2 gap-x-4">
                            <div>V00000000000123</div>
                            <div>022OT-B</div>
                            <div>Inpatient</div>
                            <div class="flex items-center">
                                <span>LOS:2</span>
                                <div class="mx-2 h-4 border-l-2"></div>
                                <span>GLOS:4</span>
                            </div>
                        </div>
                        <div class="mt-2 flex items-center">
                            <div>
                                <img src="assets/icons/icon-hc-Stethoscope-Doctor.svg" class="img-icon">
                            </div>
                            <div class="ml-2">
                                Zebra Cantaloupe, Tiger Cherry
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Encounter details Popup -->
<div *ngIf="showEncounterDetailsPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header">
            <button class="close-btn" (click)="closeEncounterDetailsPopup()">
                <img src="assets/icons/icon-caret-Left.svg" class="img-icon">
            </button>
            <button class="close-btn" (click)="closeEncounterDetailsPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-70 pt-0">
            <div class="patient-cards">
                <div class="patient-card flex flex-col">
                    <div class="p-3 border-b">
                        <span class="font-medium">Details</span>
                    </div>
                    <div class="flex flex-col p-3">
                        <table class="table table-striped">
                            <tbody>
                                <tr>
                                    <td>Account</td>
                                    <td><span class="font-medium">************</span></td>
                                </tr>
                                <tr>
                                    <td>Room</td>
                                    <td><span class="font-medium">0220T-B</span></td>
                                </tr>
                                <tr>
                                    <td>Type</td>
                                    <td><span class="font-medium">Inpatient</span></td>
                                </tr>
                                <tr>
                                    <td>DOB</td>
                                    <td><span class="font-medium">11/17/1985</span></td>
                                </tr>
                                <tr>
                                    <td>LOS</td>
                                    <td><span class="font-medium">LOS:2 GLOS:4</span></td>
                                </tr>
                                <tr>
                                    <td>Attending</td>
                                    <td><span class="font-medium">Lion Cantaloupe, Cheetah Apple</span></td>
                                </tr>
                                <tr>
                                    <td>Payor</td>
                                    <td><span class="font-medium">Humana Gold Care</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="patient-card flex flex-col">
                    <div class="p-3 border-b">
                        <span class="font-medium">CPT Codes</span>
                    </div>
                    <div class="flex flex-col p-3">
                        <div class="p-2 border-b">99396 - Preventive medicine visit, established patient, ages 40-64.
                        </div>
                        <div class="p-2">28170 - Arthrodesis, great toe.</div>
                    </div>
                </div>
                <div class="patient-card flex flex-col">
                    <div class="p-3 border-b">
                        <span class="font-medium">ICD Codes</span>
                    </div>
                    <div class="flex flex-col p-3">
                        <div class="p-2 border-b">99396 - Preventive medicine visit, established patient, ages 40-64.
                        </div>
                        <div class="p-2">28170 - Arthrodesis, great toe.</div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<!-- Attachments Popup -->
<div *ngIf="showAttachmentsPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header bg-white">
            <span>Attachments</span>
            <button class="close-btn" (click)="closeAttachmentsPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-80 min-h-80 pt-0 bg-white">
            <div class="attachment-list">
                <div class="attachment-item">
                    <div class="flex justify-between">
                        <div class="flex">
                            <div class="flex items-center">
                                <div
                                    class="flex h-50px w-50px items-center justify-center overflow-hidden rounded-md bg-primary-100">
                                    <div class="flex items-center justify-center text-sm font-medium">
                                        PDF
                                    </div>
                                </div>
                                <div class="ml-2">
                                    <div class="text-md font-medium">
                                        Lorem ipsum.pdf
                                    </div>
                                    <div class="text-secondary text-sm font-medium">
                                        Jhoette Dumlao
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="attachment-actions">
                            <button class="action-btn" (click)="closeAttachmentsPopup()">
                                <img src="assets/icons/icon-Download.svg" class="img-icon">
                            </button>
                            <button class="action-btn" (click)="closeAttachmentsPopup()">
                                <img src="assets/icons/icon-eye.svg" class="img-icon">
                            </button>
                        </div>
                    </div>
                    <div class="flex mt-2">
                        Elit neque amet urna nisl arcu. Vitae enim pellentesque consequat amet neque laoreet
                        posuere. Facilisis sit integer in mi in.
                    </div>
                </div>
                <div class="attachment-item">
                    <div class="flex justify-between">
                        <div class="flex">
                            <div class="flex items-center">
                                <div
                                    class="flex h-50px w-50px items-center justify-center overflow-hidden rounded-md bg-primary-100">
                                    <div class="flex items-center justify-center text-sm font-medium">
                                        PDF
                                    </div>
                                </div>
                                <div class="ml-2">
                                    <div class="text-md font-medium">
                                        Lorem ipsum.pdf
                                    </div>
                                    <div class="text-secondary text-sm font-medium">
                                        Jhoette Dumlao
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="attachment-actions">
                            <button class="action-btn" (click)="closeAttachmentsPopup()">
                                <img src="assets/icons/icon-Download.svg" class="img-icon">
                            </button>
                            <button class="action-btn" (click)="closeAttachmentsPopup()">
                                <img src="assets/icons/icon-eye.svg" class="img-icon">
                            </button>
                        </div>
                    </div>
                    <div class="flex mt-2">
                        Elit neque amet urna nisl arcu. Vitae enim pellentesque consequat amet neque laoreet
                        posuere. Facilisis sit integer in mi in.
                    </div>
                </div>
            </div>
            <!-- Upload Section -->
            <div class="upload-section upload-footer">
                <label class="file-upload-label">
                    <input type="file" style="display:none" />
                    <span class="file-upload-text">
                        <img src="assets/icons/icon-PlusCircle-s.svg" class="upload-icon" />
                        Click here to browse files
                    </span>
                </label>
                <input type="text" class="form-control" placeholder="Comment Optional" />
                <button class="upload-btn">Upload</button>
            </div>
        </div>

    </div>
</div>

<!-- CPT Codes Popup -->
<div *ngIf="showCPTCodesPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header bg-white px-4 py-3">
            <div class="flex flex-col">
                <span>CPT Codes</span>
                <span class="actions-subtitle">Optional description</span>
            </div>
            <button class="close-btn" (click)="closeCPTCodesPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-80 min-h-80 p-0 bg-white">
            <div class="w-full">
                <mat-tab-group>
                    <mat-tab label="Favorites">
                        <ng-template matTabContent>
                            <div class="p-3">
                                <div class="search-container">
                                    <div class="search-input-wrapper">
                                        <i class="fas fa-search search-icon"></i>
                                        <input type="text" class="search-input" placeholder="Search by keyword">
                                    </div>
                                </div>
                                <div class="flex flex-col">
                                    <div class="flex">
                                        <mat-checkbox [color]="'primary'">
                                            <span>99396 - Preventive medicine visit</span>
                                        </mat-checkbox>
                                        <div class="flex items-center ml-2">
                                            <img src="assets/icons/icon-star-favorite.svg" class="img-icon">
                                        </div>
                                    </div>
                                    <div class="flex">
                                        <mat-checkbox [color]="'primary'">
                                            <span>99396 - Preventive medicine visit</span>
                                        </mat-checkbox>
                                        <div class="flex items-center ml-2">
                                            <img src="assets/icons/icon-star-favorite.svg" class="img-icon">
                                        </div>
                                    </div>
                                    <div class="flex">
                                        <mat-checkbox [color]="'primary'">
                                            <span>99396 - Preventive medicine visit</span>
                                        </mat-checkbox>
                                        <div class="flex items-center ml-2">
                                            <img src="assets/icons/icon-star-favorite.svg" class="img-icon">
                                        </div>
                                    </div>
                                    <div class="flex">
                                        <mat-checkbox [color]="'primary'">
                                            <span>99396 - Preventive medicine visit</span>
                                        </mat-checkbox>
                                        <div class="flex items-center ml-2">
                                            <img src="assets/icons/icon-star-favorite.svg" class="img-icon">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </mat-tab>
                    <mat-tab label="All">
                        <ng-template matTabContent>
                            <div class="p-3">
                                <div class="search-container">
                                    <div class="search-input-wrapper">
                                        <i class="fas fa-search search-icon"></i>
                                        <input type="text" class="search-input" placeholder="Search by keyword">
                                    </div>
                                </div>
                                <div class="flex flex-col">
                                    <div class="flex">
                                        <mat-checkbox [color]="'primary'">
                                            <span>99396 - Preventive medicine visit</span>
                                        </mat-checkbox>
                                        <div class="flex items-center ml-2">
                                            <img src="assets/icons/icon-star-default.svg" class="img-icon">
                                        </div>
                                    </div>
                                    <div class="flex">
                                        <mat-checkbox [color]="'primary'">
                                            <span>99396 - Preventive medicine visit</span>
                                        </mat-checkbox>
                                        <div class="flex items-center ml-2">
                                            <img src="assets/icons/icon-star-default.svg" class="img-icon">
                                        </div>
                                    </div>
                                    <div class="flex">
                                        <mat-checkbox [color]="'primary'">
                                            <span>99396 - Preventive medicine visit</span>
                                        </mat-checkbox>
                                        <div class="flex items-center ml-2">
                                            <img src="assets/icons/icon-star-default.svg" class="img-icon">
                                        </div>
                                    </div>
                                    <div class="flex">
                                        <mat-checkbox [color]="'primary'">
                                            <span>99396 - Preventive medicine visit</span>
                                        </mat-checkbox>
                                        <div class="flex items-center ml-2">
                                            <img src="assets/icons/icon-star-default.svg" class="img-icon">
                                        </div>
                                    </div>
                                    <div class="flex">
                                        <mat-checkbox [color]="'primary'">
                                            <span>99396 - Preventive medicine visit</span>
                                        </mat-checkbox>
                                        <div class="flex items-center ml-2">
                                            <img src="assets/icons/icon-star-default.svg" class="img-icon">
                                        </div>
                                    </div>
                                    <div class="flex">
                                        <mat-checkbox [color]="'primary'">
                                            <span>99396 - Preventive medicine visit</span>
                                        </mat-checkbox>
                                        <div class="flex items-center ml-2">
                                            <img src="assets/icons/icon-star-default.svg" class="img-icon">
                                        </div>
                                    </div>
                                    <div class="flex">
                                        <mat-checkbox [color]="'primary'">
                                            <span>99396 - Preventive medicine visit</span>
                                        </mat-checkbox>
                                        <div class="flex items-center ml-2">
                                            <img src="assets/icons/icon-star-default.svg" class="img-icon">
                                        </div>
                                    </div>
                                    <div class="flex">
                                        <mat-checkbox [color]="'primary'">
                                            <span>99396 - Preventive medicine visit</span>
                                        </mat-checkbox>
                                        <div class="flex items-center ml-2">
                                            <img src="assets/icons/icon-star-default.svg" class="img-icon">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </mat-tab>
                </mat-tab-group>
            </div>
            <div class="upload-section upload-footer">
                <button class="upload-btn">Add</button>
            </div>
        </div>

    </div>
</div>

<!-- Submit Encounter Popup -->
<div *ngIf="showSubmitEncounterPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header bg-white">
            <span>Submit Encounter</span>
            <button class="close-btn" (click)="closeSubmitEncounterPop()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-80 min-h-80 pt-0 bg-white">
            <div class="attachment-list">
                <div class="filter-tabs">
                    <div class="facility-dropdown">
                        <select class="form-control">
                            <option>Select Physician</option>
                            <option>Physician 1</option>
                            <option>Physician 2</option>
                        </select>
                    </div>
                </div>
                <div class="filter-tabs">
                    <div class="facility-dropdown">
                        <mat-form-field>
                            <mtx-datetimepicker #datetimePicker5 [type]="type" [mode]="mode" [multiYearSelector]="multiYearSelector"
                                [startView]="startView" [twelvehour]="twelvehour" [timeInterval]="timeInterval" [touchUi]="touchUi"
                                [timeInput]="timeInput">
                            </mtx-datetimepicker>
                            <input [mtxDatetimepicker]="datetimePicker5" [max]="maxDateTime" class="form-row"
                                [(ngModel)]="testData.encounterSeenDate" matInput required>
                            <mtx-datetimepicker-toggle [for]="datetimePicker5" matSuffix></mtx-datetimepicker-toggle>
                        </mat-form-field>
                    </div>
                </div>
                <div class="filter-tabs">
                    <div class="facility-dropdown">
                       <div class="row">
                        <div class="col-2">
                            <label class="switch mt-2"><input type="checkbox" [(ngModel)]="isMultipleEncounter"
                                    class="success"><span class="slider round"></span></label>
                        </div>
                        <div class="col-8">
                            <mat-form-field class="w-100">
                                <ngx-multiple-dates [matDatepicker]="picker" name="excludedDates"
                                    [(ngModel)]="listOfEncounterSeenDates" [min]='minDate' [max]='maxDate'>
                                </ngx-multiple-dates>
                                <mat-datepicker-toggle matPrefix [for]="picker"></mat-datepicker-toggle>
                                <mat-datepicker #picker></mat-datepicker>
                            </mat-form-field>
                        </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Upload Section -->
            <div class="upload-section upload-footer">
                <button class="upload-btn">Submit</button>
            </div>
        </div>

    </div>
</div>