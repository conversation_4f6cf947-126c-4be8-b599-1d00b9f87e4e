<div class="patient-cards p-3">
    <div class="patient-card flex flex-col" *ngFor="let item of listOfPatientHistory">
        <div class="text-center bg-gray-200 p-2">
            {{item.encounterseendate}}
        </div>
        <div class="flex flex-col p-3">
            <div class="flex">
                <span class="text-lg font-medium">{{patient.patient_Name}}</span>
                <div class="mx-2 h-6 border-l-2"></div>
                <span class="font-normal">{{patient.dbo}} ({{patient.age}} {{patient.sex}})</span>
                <div class="mx-2 h-6 border-l-2"></div>
                <span class="font-normal">{{patient.facility_Name}}</span>
            </div>
            <div class="my-2 h-1 w-12 border-t-2"></div>
            <div class="grid w-full grid-cols-2 gap-x-4">
                <div>{{patient.account_Number}}</div>
                <div>{{patient.room_Number}}</div>
                <div>{{patient.admission_Type}}</div>
                <div class="flex items-center">
                    <span>{{patient.arithmetic_Mean_LOS}}</span>
                    <!-- <div class="mx-2 h-4 border-l-2"></div>
                    <span>GLOS:4</span> -->
                </div>
            </div>
            <div class="mt-2 flex items-center">
                <div>
                    <img src="assets/icons/icon-hc-Stethoscope-Doctor.svg" class="img-icon">
                </div>
                <div class="ml-2">
                    {{item.physicianname}}
                </div>
            </div>
        </div>
    </div>
</div>