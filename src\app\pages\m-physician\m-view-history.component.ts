import { Component, Input } from '@angular/core';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { PhysicianService } from 'src/app/services/physician/physician.service';
declare let $: any;

@Component({
  selector: 'app-m-view-history',
  templateUrl: './m-view-history.component.html',
  styleUrls: ['./m-physician.scss']
})
export class MViewHistoryComponent {
  public request: any = {};
  public listOfHistory: any = {};
  @Input() listOfPatientHistory: Array<any> = [];
  @Input() historyTotalCount: number = 0;
  @Input() patient: any = {};
  public historyLimit: number = 10;
  public increaseCount: number = 10;

  constructor(private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService, private readonly appComp: AppComponent, private readonly physicianService: PhysicianService) { }


  viewEncoutnerHistory(envId) {
    this.commonServ.startLoading();
    this.request.sEncounterId = this.encrDecr.set(envId);
    this.commonServ.getEncouterHistory(this.request).subscribe((p: any) => {
      this.request = {};
      this.listOfHistory = p;
      this.commonServ.stopLoading();
      $('#viewPntHistory').hide();
      $('#viewEnvHistory').show();
    }, error => {
      this.request = {};
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  backToPatientHistory() {
    this.commonServ.startLoading();
    $('#viewEnvHistory').hide();
    $('#viewPntHistory').show();
    this.commonServ.stopLoading();
  }

  viewHistory(pObj) {
    this.commonServ.startLoading();
    this.increaseCount = this.increaseCount + this.historyLimit;
    this.request.patientaccountnumber = this.encrDecr.set(pObj.account_Number);
    this.request.facilityname = this.encrDecr.set(pObj.facility_Name);
    this.request.pagenumber = this.encrDecr.set(this.increaseCount);
    this.physicianService.getPatientHistory(this.request).subscribe((p: any) => {
      this.request = {};
      this.listOfPatientHistory = p.listofViewPatientHistory;
      this.historyTotalCount = p.totalcount;
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
      console.error(error.status);
    });
    $("body").click();
  }


}
