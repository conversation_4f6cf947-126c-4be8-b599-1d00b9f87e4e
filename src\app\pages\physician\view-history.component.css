/* Mobile Responsive Styles for View History Component */

/* Mobile modal responsiveness */
@media (max-width: 767px) {
    .mobile-responsive-modal {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }

    .mobile-responsive-modal .modal-content {
        border-radius: 8px;
        max-height: 95vh;
        overflow: hidden;
    }

    .mobile-responsive-modal .modal-header {
        padding: 0.75rem 1rem;
        background-color: #0169ab;
        color: white;
        border-bottom: 1px solid #e3e6f0;
    }

    .mobile-responsive-modal .modal-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: white;
    }

    .mobile-responsive-modal .modal-body {
        padding: 0.75rem;
        max-height: 80vh;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }

    .mobile-responsive-modal .closePopup {
        color: white;
        font-size: 1.5rem;
        cursor: pointer;
        opacity: 1;
    }

    .mobile-responsive-modal .closePopup:hover {
        opacity: 0.8;
    }
}

/* Desktop styles - default table layout */
.patient-history-table {
    width: 100%;
}

.patient-history-table th,
.patient-history-table td {
    text-align: center;
    padding: 0.5rem;
}

/* Mobile-specific styles for merged patient information display */
@media (max-width: 767px) {
    /* Hide the original table headers and restructure for mobile */
    .patient-history-table thead {
        display: none;
    }

    .patient-history-table tbody {
        display: block;
    }

    .patient-history-table tbody tr {
        display: block;
        margin-bottom: 1rem;
        border: 1px solid #e3e6f0;
        border-radius: 8px;
        padding: 0.75rem;
        background-color: #fff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
    }

    .patient-history-table tbody td {
        display: block;
        text-align: left;
        padding: 0.25rem 0;
        border: none;
    }

    /* Hide desktop table cells on mobile */
    .patient-history-table tbody td:nth-child(2),
    .patient-history-table tbody td:nth-child(3),
    .patient-history-table tbody td:nth-child(4),
    .patient-history-table tbody td:nth-child(5) {
        display: none !important;
    }

    /* Style the View button cell */
    .patient-history-table tbody td:first-child {
        margin-bottom: 0.5rem;
        text-align: center;
        display: block !important;
    }

    /* Create merged patient info display */
    .mobile-patient-info {
        display: block !important;
        padding: 0.5rem 0;
        line-height: 1.4;
        font-size: 0.95rem;
        border: none !important;
        text-align: left !important;
    }

    .mobile-patient-info .patient-name {
        display: block;
        font-weight: 600;
        color: #336699;
        margin-bottom: 0.25rem;
        font-size: 1rem;
    }

    .mobile-patient-info .account-number {
        display: inline;
        color: #666;
        /*margin-right: 0.5rem;*/
        font-size: 0.9rem;
    }

    .mobile-patient-info .provider-name {
        display: inline;
        font-weight: bold;
        color: #333;
        font-size: 0.9rem;
    }

    .mobile-patient-info .separator {
        color: #999;
        margin: 0 0.25rem;
        font-weight: normal;
    }

    .mobile-patient-info .patient-details {
        margin-bottom: 0.25rem;
    }

    .mobile-patient-info .encounter-date {
        display: block;
        color: #666;
        font-size: 0.85rem;
        font-style: italic;
        margin-top: 0.25rem;
    }
    
    /* Enhanced button styling for mobile */
    .patient-history-table .btn-outline-info {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
        border-radius: 6px;
        min-width: 80px;
    }
    
    /* Load More button styling for mobile */
    .load-more-mobile {
        text-align: center;
        padding: 1rem;
        border: 1px solid #e3e6f0;
        border-radius: 8px;
        background-color: #f8f9fc;
        margin-top: 0.5rem;
    }
    
    .load-more-mobile .btn-info {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
        border-radius: 6px;
    }
}

/* iPhone specific optimizations */
@media (max-width: 767px) and (-webkit-min-device-pixel-ratio: 2) {
    .mobile-patient-info {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    
    .patient-history-table .btn-outline-info {
        -webkit-appearance: none;
        appearance: none;
        -webkit-tap-highlight-color: transparent;
    }
}

/* Android specific optimizations */
@media (max-width: 767px) and (min-resolution: 192dpi) {
    .mobile-patient-info .patient-name {
        font-weight: 700; /* Slightly bolder for Android */
    }
    
    .mobile-patient-info .provider-name {
        font-weight: 800; /* Extra bold for Android */
    }
}

/* Very small screens (iPhone SE, etc.) */
@media (max-width: 375px) {
    .patient-history-table tbody tr {
        padding: 0.5rem;
    }
    
    .mobile-patient-info {
        font-size: 0.9rem;
    }
    
    .patient-history-table .btn-outline-info {
        font-size: 0.8rem;
        padding: 0.3rem 0.6rem;
        min-width: 70px;
    }
}

/* Mobile patient details container styles */
@media (max-width: 767px) {
    .mobile-patient-details-container {
        padding: 0.5rem;
        background-color: #f8f9fc;
        border-radius: 8px;
        border: 1px solid #e3e6f0;
        margin: 0.5rem 0;
    }

    .mobile-patient-details-container .callout {
        margin: 0.25rem;
        padding: 0.5rem;
        border-radius: 6px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        background-color: #fff !important;
        border-left-width: 3px;
        border-left-color: #428bca;
        min-width: calc(50% - 0.5rem);
        max-width: 100%;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    .mobile-patient-details-container .callout h6 {
        margin: 0;
        font-size: 0.875rem !important;
        line-height: 1.3;
        color: #333;
        font-weight: 600;
    }

    .mobile-patient-details-container .callout h6 span {
        font-weight: 700;
        color: #0169ab;
        word-break: break-word;
        display: inline-block;
        max-width: 100%;
    }

    /* Ensure proper wrapping and spacing */
    .mobile-patient-details-container .m-marbtn {
        margin: 0.25rem !important;
        vertical-align: top;
        display: inline-block;
        width: calc(50% - 0.5rem);
        box-sizing: border-box;
    }

    /* Full width for longer content */
    .mobile-patient-details-container .m-marbtn:nth-child(3n),
    .mobile-patient-details-container .m-marbtn:last-child {
        width: calc(100% - 0.5rem);
    }
}

/* iPhone specific optimizations for patient details */
@media (max-width: 767px) and (-webkit-min-device-pixel-ratio: 2) {
    .mobile-patient-details-container .callout h6 {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .mobile-patient-details-container .callout {
        -webkit-tap-highlight-color: transparent;
    }
}

/* Android specific optimizations for patient details */
@media (max-width: 767px) and (min-resolution: 192dpi) {
    .mobile-patient-details-container .callout h6 {
        font-weight: 700;
    }

    .mobile-patient-details-container .callout h6 span {
        font-weight: 800;
    }
}

/* Very small screens (iPhone SE, etc.) */
@media (max-width: 375px) {
    .mobile-patient-details-container .callout {
        margin: 0.2rem;
        padding: 0.4rem;
        min-width: calc(100% - 0.4rem);
        width: calc(100% - 0.4rem);
    }

    .mobile-patient-details-container .m-marbtn {
        width: calc(100% - 0.4rem);
        margin: 0.2rem !important;
    }

    .mobile-patient-details-container .callout h6 {
        font-size: 0.8rem !important;
    }
}

/* Landscape orientation adjustments for mobile */
@media (max-width: 767px) and (orientation: landscape) {
    .patient-history-table tbody tr {
        margin-bottom: 0.75rem;
        padding: 0.6rem;
    }

    .mobile-patient-info {
        font-size: 0.9rem;
    }

    .mobile-patient-details-container {
        padding: 0.4rem;
        margin: 0.3rem 0;
    }

    .mobile-patient-details-container .callout {
        margin: 0.2rem;
        padding: 0.4rem;
    }

    .mobile-patient-details-container .callout h6 {
        font-size: 0.8rem !important;
    }
}
